Iteration 2
A. Användarhantering
• Instructor:
o Id
o Name
o Role
o Email
o (Admin Generated) Password
o Telephone
• Member:
o Id
o Name
o Role
o Email
o (User Generated) Password
o Telephone
• Registrera konto med e-post och lösenord
o Admin registers an instructor
o Member registers themselves
• <PERSON><PERSON> personuppgifter (members only)
• Se egen profil och kommande bokade pass
o Profile Page
B. Bokningssystem
• Lista över alla kommande träningspass
o ”My Bookings” page
• Visa datum, tid, plats, instruktör och antal lediga platser.
• Möjlighet att boka ett pass
o Confirmation of booking
• Möjlighet att avboka pass
o Confirmation of cancellation
• Bekräftelse på bokning och avbokning (via skärm eller mejl).
C. Pass- och Schemahantering
• Administratörer kan skapa, redigera och ta bort pass.
• Varje pass har:
o Titel
o Beskrivning
o Tid och datum
o Plats
o Instruktör
o Max antal deltagare
o Visa alla pass i en kalenderöversikt.
F. Mobilapplikation / Responsiv Design
• Webbapplikationen måste vara responsiv och fungera på mobil, surfplatta och dator.
