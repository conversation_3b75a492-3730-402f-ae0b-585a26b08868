InstructorService.sln
│
├── InstructorService.Application/
│   ├── Models/
│   │   └── Instructor.cs
│   ├── DTOs/
│   │   └── InstructorDto.cs
│   ├── Interfaces/
│   │   └── IInstructorRepository.cs
│   ├── Services/
│   │   └── InstructorService.cs
│
├── InstructorService.Persistence/
│   ├── Data/
│   │   └── AppDbContext.cs
│   ├── Repositories/
│   │   └── InstructorRepository.cs
│   ├── Configurations/
│   │   └── InstructorConfiguration.cs
│   └── DependencyInjection.cs
│
└── InstructorService.Presentation/   # Web API
├── Controllers/
│   └── InstructorController.cs
└── Program.cs